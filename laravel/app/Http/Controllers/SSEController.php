<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SSEController extends Controller
{
    /**
     * The SSE endpoint for progress tracking
     *
     * @return \Symfony\Component\HttpFoundation\StreamedResponse|\Illuminate\Http\JsonResponse
     */
    public function progressStream()
    {
        // Verify user is authenticated
        if (!auth()->check()) {
            return response()->json(['error' => 'Unauthenticated'], 401);
        }

        $user = auth()->user();
        Log::info("SSE: Starting progress stream for user {$user->id}");

        return response()->stream(function () use ($user) {
            // Set execution time limit to prevent timeout
            set_time_limit(0);

            try {
                Log::info("SSE: Stream callback started for user {$user->id}");

                // Send initial connection message with user info
                $this->sendSSEEventOctane('connected', [
                    'connected' => true,
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'timestamp' => now()->timestamp
                ]);

                // Get the user ID for user-specific progress tracking
                $userId = $user->id;
                $cacheKey = "sse_progress_{$userId}";

                // Keep the connection open and check for progress updates
                $retry = 3000; // Retry interval in milliseconds
                $maxIterations = 7200; // Maximum 1 hour (7200 * 0.5 seconds)
                $iteration = 0;

                while ($iteration < $maxIterations) {
                    // Check if client closed the connection
                    if (connection_aborted()) {
                        Log::info("SSE: Client disconnected for user {$userId}");
                        break;
                    }

                    // Get progress data from cache
                    $progressData = Cache::get($cacheKey, []);

                    // Send any new progress events
                    if (!empty($progressData)) {
                        foreach ($progressData as $requestId => $data) {
                            // Only send if we haven't sent this event before
                            if (!isset($data['sent']) || !$data['sent']) {
                                $this->sendSSEEventOctane('progress', [
                                    'requestId' => $requestId,
                                    'progress' => $data['progress'] ?? 0,
                                    'message' => $data['message'] ?? '',
                                    'isComplete' => $data['isComplete'] ?? false,
                                    'timestamp' => now()->timestamp
                                ]);

                                // Mark as sent
                                $progressData[$requestId]['sent'] = true;
                                Cache::put($cacheKey, $progressData, now()->addMinutes(30));

                                // If this event is marked as complete, remove it from the cache
                                if ($data['isComplete'] ?? false) {
                                    unset($progressData[$requestId]);
                                    Cache::put($cacheKey, $progressData, now()->addMinutes(30));
                                }
                            }
                        }
                    }

                    // Send heartbeat every 30 seconds
                    if ($iteration % 60 === 0) {
                        $this->sendSSEEventOctane('heartbeat', [
                            'timestamp' => now()->timestamp,
                            'iteration' => $iteration
                        ]);
                    }

                    // Send retry directive periodically
                    if ($iteration % 6 === 0) { // Every 3 seconds
                        echo "retry: {$retry}\n\n";
                        flush();
                    }

                    // Sleep for a short time to prevent CPU overuse
                    usleep(500000); // 500ms
                    $iteration++;
                }

                Log::info("SSE: Stream ended for user {$userId} after {$iteration} iterations");

            } catch (\Exception $e) {
                Log::error('SSE Error: ' . $e->getMessage(), [
                    'exception' => $e,
                    'user_id' => $user->id ?? 'unauthenticated',
                    'trace' => $e->getTraceAsString()
                ]);

                // Send error event
                $this->sendSSEEventOctane('error', [
                    'error' => 'An error occurred',
                    'timestamp' => now()->timestamp
                ]);
            }
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no',
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Headers' => 'Cache-Control, Authorization',
            'Access-Control-Allow-Credentials' => 'true'
        ]);
    }

    /**
     * Send an SSE event with proper formatting (Octane compatible)
     *
     * @param string $event
     * @param array $data
     * @return void
     */
    private function sendSSEEventOctane(string $event, array $data): void
    {
        $output = "event: {$event}\n";
        $output .= "data: " . json_encode($data) . "\n\n";

        Log::info("SSE: Sending event", ['event' => $event, 'data' => $data]);

        echo $output;
        flush(); // Simple flush for Octane
    }

    /**
     * Send an SSE event with proper formatting (Legacy)
     *
     * @param string $event
     * @param array $data
     * @return void
     */
    private function sendSSEEvent(string $event, array $data): void
    {
        $output = "event: {$event}\n";
        $output .= "data: " . json_encode($data) . "\n\n";

        Log::info("SSE: Sending event", ['event' => $event, 'data' => $data]);

        echo $output;
        $this->flushOutput();
    }

    /**
     * Flush output to client (Legacy)
     *
     * @return void
     */
    private function flushOutput(): void
    {
        Log::info("SSE: Flushing output");

        // Try different methods to ensure output is sent
        if (function_exists('fastcgi_finish_request')) {
            Log::info("SSE: Using fastcgi_finish_request");
            fastcgi_finish_request();
        }

        if (ob_get_level()) {
            Log::info("SSE: Using ob_flush, level: " . ob_get_level());
            ob_flush();
        }

        flush();
        Log::info("SSE: Flush completed");
    }


    /**
     * Register a new progress tracking request
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function registerProgress(Request $request)
    {
        $requestId = $request->input('requestId', 'default');
        $userId = auth()->id();
        $cacheKey = "sse_progress_{$userId}";

        // Get existing progress data or initialize empty array
        $progressData = Cache::get($cacheKey, []);

        // Add new request with initial progress
        $progressData[$requestId] = [
            'progress' => 0,
            'message' => 'Starting...',
            'isComplete' => false,
            'sent' => false,
            'created_at' => now()->timestamp
        ];

        // Store in cache
        Cache::put($cacheKey, $progressData, now()->addMinutes(30));

        return response()->json(['success' => true]);
    }

    /**
     * Unregister a progress tracking request
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unregisterProgress(Request $request)
    {
        $requestId = $request->input('requestId', 'default');
        $userId = auth()->id();
        $cacheKey = "sse_progress_{$userId}";

        // Get existing progress data
        $progressData = Cache::get($cacheKey, []);

        // Mark the request as complete
        if (isset($progressData[$requestId])) {
            $progressData[$requestId]['isComplete'] = true;
            $progressData[$requestId]['progress'] = 100;
            $progressData[$requestId]['message'] = 'Completed';
            $progressData[$requestId]['sent'] = false; // Ensure the completion event is sent

            // Store in cache
            Cache::put($cacheKey, $progressData, now()->addMinutes(30));
        }

        return response()->json(['success' => true]);
    }

    /**
     * Update progress for a specific request
     * This can be called from other parts of the application
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProgress(Request $request)
    {
        $requestId = $request->input('requestId', 'default');
        $progress = $request->input('progress', 0);
        $message = $request->input('message', '');
        $isComplete = $request->input('isComplete', false);
        $userId = $request->input('userId', auth()->id());

        $cacheKey = "sse_progress_{$userId}";

        // Get existing progress data
        $progressData = Cache::get($cacheKey, []);

        // Update progress
        $progressData[$requestId] = [
            'progress' => $progress,
            'message' => $message,
            'isComplete' => $isComplete,
            'sent' => false, // Mark as not sent so it will be sent in the next cycle
            'updated_at' => now()->timestamp
        ];

        // Store in cache
        Cache::put($cacheKey, $progressData, now()->addMinutes(30));

        return response()->json(['success' => true]);
    }
}
